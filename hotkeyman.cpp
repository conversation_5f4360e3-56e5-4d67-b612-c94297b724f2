#include "hotkeyman.h"
#include <QAbstractNativeEventFilter>
#include <QApplication>

#ifdef Q_OS_WIN
#include <QWindow>
#endif

class NativeEventFilter : public QAbstractNativeEventFilter
{
public:
    NativeEventFilter(HotkeyMan* manager) : manager(manager) {}

    virtual bool nativeEventFilter(const QByteArray &eventType, void *message, qintptr *result) override {
        return manager->nativeEventFilter(eventType, message, result);
    }

private:
    HotkeyMan* manager;
};

HotkeyMan::HotkeyMan(QObject *parent)
    : QObject(parent), m_registered(false)
{
    // 注册本类为原生事件过滤器
    qApp->installNativeEventFilter(new NativeEventFilter(this));

#ifdef Q_OS_WIN
    // 创建一个隐藏窗口以接收消息
    m_hwnd = CreateWindowEx(
        0, L"STATIC", L"HotkeyWindow",
        0, 0, 0, 0, 0,
        HWND_MESSAGE, NULL, GetModuleHandle(NULL), NULL);

    if (m_hwnd == NULL) {
        qDebug() << "无法创建隐藏窗口用于热键监听";
    } else {
        qDebug() << "已创建热键监听窗口, HWND =" << m_hwnd;
    }
#endif
}

HotkeyMan::~HotkeyMan()
{
    unregisterHotkey();

#ifdef Q_OS_WIN
    if (m_hwnd) {
        DestroyWindow(m_hwnd);
        m_hwnd = NULL;
    }
#endif
}

#ifdef Q_OS_WIN
void HotkeyMan::parseHotkeyString(const QString &hotkeyStr, UINT &modifiers, UINT &vk)
{
    modifiers = 0;
    vk = 0;

    // 解析修饰键
    if (hotkeyStr.contains("Ctrl", Qt::CaseInsensitive)) {
        modifiers |= MOD_CONTROL;
    }
    if (hotkeyStr.contains("Alt", Qt::CaseInsensitive)) {
        modifiers |= MOD_ALT;
    }
    if (hotkeyStr.contains("Shift", Qt::CaseInsensitive)) {
        modifiers |= MOD_SHIFT;
    }
    if (hotkeyStr.contains("Win", Qt::CaseInsensitive)) {
        modifiers |= MOD_WIN;
    }

    // 解析主键 - 支持F1-F12功能键
    if (hotkeyStr.contains("F1", Qt::CaseInsensitive)) {
        vk = VK_F1;
    } else if (hotkeyStr.contains("F2", Qt::CaseInsensitive)) {
        vk = VK_F2;
    } else if (hotkeyStr.contains("F3", Qt::CaseInsensitive)) {
        vk = VK_F3;
    } else if (hotkeyStr.contains("F4", Qt::CaseInsensitive)) {
        vk = VK_F4;
    } else if (hotkeyStr.contains("F5", Qt::CaseInsensitive)) {
        vk = VK_F5;
    } else if (hotkeyStr.contains("F6", Qt::CaseInsensitive)) {
        vk = VK_F6;
    } else if (hotkeyStr.contains("F7", Qt::CaseInsensitive)) {
        vk = VK_F7;
    } else if (hotkeyStr.contains("F8", Qt::CaseInsensitive)) {
        vk = VK_F8;
    } else if (hotkeyStr.contains("F9", Qt::CaseInsensitive)) {
        vk = VK_F9;
    } else if (hotkeyStr.contains("F10", Qt::CaseInsensitive)) {
        vk = VK_F10;
    } else if (hotkeyStr.contains("F11", Qt::CaseInsensitive)) {
        vk = VK_F11;
    } else if (hotkeyStr.contains("F12", Qt::CaseInsensitive)) {
        vk = VK_F12;
    } else {
        // 字母键
        for (char c = 'A'; c <= 'Z'; c++) {
            QString letter = QString(QChar(c));
            if (hotkeyStr.contains("+" + letter, Qt::CaseInsensitive)) {
                vk = (UINT)c;
                break;
            }
        }
    }
}
#endif

bool HotkeyMan::registerHotkey(const QString &hotkeyStr, const std::function<void()> &callback)
{
    unregisterHotkey(); // 先注销之前的热键
    m_callback = callback;

#ifdef Q_OS_WIN
    if (!m_hwnd) {
        qDebug() << "没有有效的窗口句柄，无法注册热键";
        return false;
    }

    UINT modifiers = 0;
    UINT vk = 0;

    parseHotkeyString(hotkeyStr, modifiers, vk);

    if (vk == 0) {
        qDebug() << "无效的热键:" << hotkeyStr;
        return false;
    }

    if (!RegisterHotKey(m_hwnd, HOTKEY_ID, modifiers, vk)) {
        DWORD error = GetLastError();
        qDebug() << "注册热键失败:" << hotkeyStr << "错误码:" << error;
        return false;
    }

    qDebug() << "成功注册热键:" << hotkeyStr << "修饰符:" << modifiers << "键码:" << vk;
    m_registered = true;
    return true;
#else
    qDebug() << "当前平台不支持全局热键";
    return false;
#endif
}

void HotkeyMan::unregisterHotkey()
{
#ifdef Q_OS_WIN
    if (m_registered && m_hwnd) {
        UnregisterHotKey(m_hwnd, HOTKEY_ID);
        m_registered = false;
        qDebug() << "已注销热键";
    }
#endif
}

bool HotkeyMan::nativeEventFilter(const QByteArray &eventType, void *message, qintptr *result)
{
#ifdef Q_OS_WIN
    if (eventType == "windows_generic_MSG" || eventType == "windows_dispatcher_MSG") {
        MSG *msg = static_cast<MSG*>(message);
        if (msg->message == WM_HOTKEY && msg->wParam == HOTKEY_ID) {
            qDebug() << "接收到热键消息! hwnd=" << msg->hwnd;

            // 使用延迟调用来避免死锁或其他消息处理问题
            QTimer::singleShot(0, [this]() {
                emit hotkeyPressed();
                if (m_callback) {
                    m_callback();
                }
            });

            return true;
        }
    }
#endif
    return false;
}
