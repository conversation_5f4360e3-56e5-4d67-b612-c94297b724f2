cmake_minimum_required(VERSION 3.31)
project(tailchat_web)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# MSVC设置UTF-8编码
add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")

set(CMAKE_PREFIX_PATH "C:/Qt/6.9.1/msvc2022_64/lib/cmake")

find_package(Qt6 COMPONENTS
        Core
        Gui
        Widgets
        WebEngineWidgets
        Network
        REQUIRED)

add_executable(tailchat_web WIN32
        main.cpp
        browser.cpp
        browser.h
        config.cpp
        config.h
        hotkeyman.cpp
        hotkeyman.h)

target_link_libraries(tailchat_web
        Qt::Core
        Qt::Gui
        Qt::Widgets
        Qt::WebEngineWidgets
        Qt::Network)
