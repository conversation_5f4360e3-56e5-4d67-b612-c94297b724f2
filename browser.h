#ifndef BROWSER_H
#define BROWSER_H

#include <QMainWindow>
#include <QWebEngineView>
#include <QWebEngineProfile>
#include <QWebEngineCookieStore>
#include <QWebEngineSettings>
#include <QSystemTrayIcon>
#include <QMenu>
#include <QAction>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLineEdit>
#include <QShortcut>
#include <QCloseEvent>
#include <QHideEvent>
#include <QShowEvent>
#include <QProgressBar>
#include <QTimer>
#include <QWidget>
#include "config.h"
#include "hotkeyman.h"

class Browser : public QMainWindow
{
    Q_OBJECT

public:
    Browser(QWidget *parent = nullptr);
    ~Browser();

protected:
    void closeEvent(QCloseEvent *event) override;
    void hideEvent(QHideEvent *event) override;
    void showEvent(QShowEvent *event) override;
    bool eventFilter(QObject *watched, QEvent *event) override;

private slots:
    void toggleVisibility();
    void trayIconActivated(QSystemTrayIcon::ActivationReason reason);
    void refreshPage();
    void navigateToUrl();
    void onUrlChanged(const QUrl &url);
    void onLoadProgress(int progress);
    void onLoadFinished(bool success);
    void showSettings();

private:
    void setupUI();
    void setupTrayIcon();
    void setupShortcuts();
    void setupStyle();
    void createMenuBar();
    void setupBrowserProfile(); // 新增：设置浏览器配置文件方法

    QWebEngineView *webView;
    QWebEngineProfile *webProfile; // 新增：WebEngine配置文件
    QSystemTrayIcon *trayIcon;
    QMenu *trayMenu;
    QLineEdit *urlEdit;
    QPushButton *refreshButton;
    QPushButton *settingsButton;
    QShortcut *toggleShortcut;
    QProgressBar *progressBar;
    QWidget *helperWindow; // 辅助窗口

    Config *config;
    bool isHiddenToTray;

    // 使用新的热键管理类
    HotkeyMan *hotkeyManager;
};

#endif // BROWSER_H
