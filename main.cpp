#include <QApplication>
#include <QWebEngineView>
#include <QSharedMemory>
#include <QMessageBox>
#include <QSystemSemaphore>
#include <QDebug>
#include "browser.h"

int main(int argc, char *argv[]) {
    QApplication a(argc, argv);

    // 设置应用程序属性
    a.setQuitOnLastWindowClosed(false); // 关闭窗口时不退出应用程序
    a.setApplicationName("TailChat Web Browser");
    a.setOrganizationName("TailChat");

    // 单例检查 - 使用共享内存确保只有一个实例运行
    const QString sharedMemoryKey = "TailChatWebBrowser_SingleInstance";

    // 使用系统信号量来处理多个进程同时启动的情况
    QSystemSemaphore semaphore("TailChatWebBrowser_Semaphore", 1);
    semaphore.acquire(); // 获取信号量

    QSharedMemory sharedMemory(sharedMemoryKey);
    bool isRunning = false;

    // 尝试连接到已存在的共享内存
    if (sharedMemory.attach()) {
        isRunning = true;
    } else {
        // 创建共享内存
        if (!sharedMemory.create(1)) {
            qDebug() << "无法创建共享内存:" << sharedMemory.errorString();
            isRunning = true; // 假设已有实例在运行
        }
    }

    semaphore.release(); // 释放信号量

    if (isRunning) {
        // 已有实例在运行，显示提示并退出
        QMessageBox::information(nullptr, "TailChat Web Browser",
                                "程序已经在运行中！\n请检查系统托盘或使用快捷键切换窗口显示。");
        return 0;
    }

    Browser browser;

    browser.resize(400, 800);
    browser.show();

    return a.exec();
}