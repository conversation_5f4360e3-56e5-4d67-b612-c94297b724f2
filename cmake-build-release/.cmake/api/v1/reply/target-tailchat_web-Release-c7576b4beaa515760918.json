{"artifacts": [{"path": "tailchat_web.exe"}, {"path": "tailchat_web.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "add_compile_options"], "files": ["CMakeLists.txt", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6WebEngineWidgets/Qt6WebEngineWidgetsTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6WebEngineWidgets/Qt6WebEngineWidgetsConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6WebEngineCore/Qt6WebEngineCoreTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6WebEngineCore/Qt6WebEngineCoreConfig.cmake", "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6WebEngineWidgets/Qt6WebEngineWidgetsDependencies.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6PrintSupport/Qt6PrintSupportTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6PrintSupport/Qt6PrintSupportConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6WebEngineCore/Qt6WebEngineCoreDependencies.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Qml/Qt6QmlTargets.cmake", "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Qml/Qt6QmlConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}, {"command": 1, "file": 0, "line": 30, "parent": 0}, {"command": 4, "file": 0, "line": 15, "parent": 0}, {"file": 3, "parent": 3}, {"command": 4, "file": 3, "line": 218, "parent": 4}, {"file": 2, "parent": 5}, {"command": 3, "file": 2, "line": 55, "parent": 6}, {"file": 1, "parent": 7}, {"command": 2, "file": 1, "line": 61, "parent": 8}, {"command": 3, "file": 2, "line": 43, "parent": 6}, {"file": 8, "parent": 10}, {"command": 6, "file": 8, "line": 45, "parent": 11}, {"command": 5, "file": 7, "line": 137, "parent": 12}, {"command": 4, "file": 6, "line": 76, "parent": 13}, {"file": 5, "parent": 14}, {"command": 3, "file": 5, "line": 55, "parent": 15}, {"file": 4, "parent": 16}, {"command": 2, "file": 4, "line": 61, "parent": 17}, {"command": 5, "file": 7, "line": 137, "parent": 12}, {"command": 4, "file": 6, "line": 76, "parent": 19}, {"file": 10, "parent": 20}, {"command": 3, "file": 10, "line": 55, "parent": 21}, {"file": 9, "parent": 22}, {"command": 2, "file": 9, "line": 61, "parent": 23}, {"command": 3, "file": 5, "line": 43, "parent": 15}, {"file": 13, "parent": 25}, {"command": 6, "file": 13, "line": 45, "parent": 26}, {"command": 5, "file": 7, "line": 137, "parent": 27}, {"command": 4, "file": 6, "line": 76, "parent": 28}, {"file": 12, "parent": 29}, {"command": 3, "file": 12, "line": 55, "parent": 30}, {"file": 11, "parent": 31}, {"command": 2, "file": 11, "line": 61, "parent": 32}, {"command": 3, "file": 12, "line": 43, "parent": 30}, {"file": 16, "parent": 34}, {"command": 6, "file": 16, "line": 45, "parent": 35}, {"command": 5, "file": 7, "line": 137, "parent": 36}, {"command": 4, "file": 6, "line": 76, "parent": 37}, {"file": 15, "parent": 38}, {"command": 3, "file": 15, "line": 55, "parent": 39}, {"file": 14, "parent": 40}, {"command": 2, "file": 14, "line": 61, "parent": 41}, {"command": 4, "file": 3, "line": 218, "parent": 4}, {"file": 18, "parent": 43}, {"command": 3, "file": 18, "line": 55, "parent": 44}, {"file": 17, "parent": 45}, {"command": 2, "file": 17, "line": 61, "parent": 46}, {"command": 5, "file": 7, "line": 137, "parent": 27}, {"command": 4, "file": 6, "line": 76, "parent": 48}, {"file": 20, "parent": 49}, {"command": 3, "file": 20, "line": 55, "parent": 50}, {"file": 19, "parent": 51}, {"command": 2, "file": 19, "line": 61, "parent": 52}, {"command": 4, "file": 3, "line": 218, "parent": 4}, {"file": 22, "parent": 54}, {"command": 3, "file": 22, "line": 57, "parent": 55}, {"file": 21, "parent": 56}, {"command": 2, "file": 21, "line": 61, "parent": 57}, {"command": 5, "file": 7, "line": 137, "parent": 36}, {"command": 4, "file": 6, "line": 76, "parent": 59}, {"file": 24, "parent": 60}, {"command": 3, "file": 24, "line": 58, "parent": 61}, {"file": 23, "parent": 62}, {"command": 2, "file": 23, "line": 61, "parent": 63}, {"command": 7, "file": 0, "line": 10, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD"}, {"backtrace": 65, "fragment": "/utf-8"}, {"backtrace": 2, "fragment": "-Zc:__cplusplus"}, {"backtrace": 2, "fragment": "-permissive-"}, {"backtrace": 2, "fragment": "-utf-8"}], "defines": [{"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_NO_DEBUG"}, {"backtrace": 2, "define": "QT_OPENGL_LIB"}, {"backtrace": 2, "define": "QT_POSITIONING_LIB"}, {"backtrace": 2, "define": "QT_PRINTSUPPORT_LIB"}, {"backtrace": 2, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 2, "define": "QT_QMLMETA_LIB"}, {"backtrace": 2, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 2, "define": "QT_QMLWORKERSCRIPT_LIB"}, {"backtrace": 2, "define": "QT_QML_LIB"}, {"backtrace": 2, "define": "QT_QUICK_LIB"}, {"backtrace": 2, "define": "QT_WEBCHANNEL_LIB"}, {"backtrace": 2, "define": "QT_WEBENGINECORE_LIB"}, {"backtrace": 2, "define": "QT_WEBENGINEWIDGETS_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}, {"backtrace": 2, "define": "UNICODE"}, {"backtrace": 2, "define": "WIN32"}, {"backtrace": 2, "define": "WIN64"}, {"backtrace": 2, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 2, "define": "_UNICODE"}, {"backtrace": 2, "define": "_WIN64"}], "includes": [{"backtrace": 0, "isSystem": true, "path": "D:/QtProject/tailchat_web/cmake-build-release/tailchat_web_autogen/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtWebEngineWidgets"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtWebEngineCore"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtNetwork"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtQuick"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtQml"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtQmlIntegration"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtQmlMeta"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtQmlModels"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtQmlWorkerScript"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtOpenGL"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtWebChannel"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtPositioning"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/6.9.1/msvc2022_64/include/QtPrintSupport"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "20"}, "sourceIndexes": [0, 1, 2, 4]}], "dependencies": [{"backtrace": 0, "id": "tailchat_web_autogen::@6890427a1f51a3e7e1df"}, {"id": "tailchat_web_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}], "id": "tailchat_web::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Core.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Gui.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Widgets.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6WebEngineWidgets.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Network.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6WebEngineCore.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6WebChannel.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Positioning.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6PrintSupport.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Quick.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6QmlMeta.lib", "role": "libraries"}, {"backtrace": 42, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6QmlWorkerScript.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6QmlModels.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6OpenGL.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\Qt\\6.9.1\\msvc2022_64\\lib\\Qt6Qml.lib", "role": "libraries"}, {"backtrace": 53, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 58, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 58, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 64, "fragment": "shell32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "tailchat_web", "nameOnDisk": "tailchat_web.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 4]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 5]}, {"name": "", "sourceIndexes": [6]}, {"name": "CMake Rules", "sourceIndexes": [7]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "cmake-build-release/tailchat_web_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "browser.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "browser.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "config.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "config.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-release/tailchat_web_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-release/tailchat_web_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}