{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "tailchat_web", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "tailchat_web::@6890427a1f51a3e7e1df", "jsonFile": "target-tailchat_web-Release-c7576b4beaa515760918.json", "name": "tailchat_web", "projectIndex": 0}, {"directoryIndex": 0, "id": "tailchat_web_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-tailchat_web_autogen-Release-db3078a141af5958ac18.json", "name": "tailchat_web_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "tailchat_web_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-tailchat_web_autogen_timestamp_deps-Release-6ca2d92d8540187b38fc.json", "name": "tailchat_web_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/QtProject/tailchat_web/cmake-build-release", "source": "D:/QtProject/tailchat_web"}, "version": {"major": 2, "minor": 7}}