{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/bin/cmake.exe", "cpack": "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/bin/cpack.exe", "ctest": "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/bin/ctest.exe", "root": "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 6, "string": "3.31.6", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-895a49464034b4bb35a7.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-293577bc0849a3976393.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-c13dd0519a1e4334e4e5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-2adc9caa00e53e1a3140.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-293577bc0849a3976393.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-c13dd0519a1e4334e4e5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-895a49464034b4bb35a7.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, "toolchains-v1": {"jsonFile": "toolchains-v1-2adc9caa00e53e1a3140.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}