"C:\Users\<USER>\AppData\Local\Programs\CLion 2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_BUILD_TYPE=Release "-DCMAKE_MAKE_PROGRAM=C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/ninja/win/x64/ninja.exe" -G Ninja -S D:\QtProject\tailchat_web -B D:\QtProject\tailchat_web\cmake-build-release
-- Could NOT find WrapVulkanHeaders (missing: Vulkan_INCLUDE_DIR) 
-- Could NOT find WrapVulkanHeaders (missing: Vulkan_INCLUDE_DIR) 
-- Configuring done (0.9s)
-- Generating done (0.2s)
-- Build files have been written to: D:/QtProject/tailchat_web/cmake-build-release
