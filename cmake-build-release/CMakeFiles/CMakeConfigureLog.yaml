
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.40.33807/bin/Hostx64/x64/cl.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.40.33811 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      CMakeCCompilerId.c
      Microsoft (R) Incremental Linker Version 14.40.33811.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
      
      /out:CMakeCCompilerId.exe 
      CMakeCCompilerId.obj 
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.obj"
      
      The C compiler identification is MSVC, found in:
        D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/3.31.6/CompilerIdC/CMakeCCompilerId.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1288 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting C compiler /showIncludes prefix:
        main.c
        注意: 包含文件:  D:\\QtProject\\tailchat_web\\cmake-build-release\\CMakeFiles\\ShowIncludes\\foo.h
        
      Found prefix "注意: 包含文件:  "
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.40.33807/bin/Hostx64/x64/cl.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.40.33811 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      CMakeCXXCompilerId.cpp
      Microsoft (R) Incremental Linker Version 14.40.33811.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
      
      /out:CMakeCXXCompilerId.exe 
      CMakeCXXCompilerId.obj 
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"
      
      The CXX compiler identification is MSVC, found in:
        D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/3.31.6/CompilerIdCXX/CMakeCXXCompilerId.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1288 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        main.c
        注意: 包含文件:  D:\\QtProject\\tailchat_web\\cmake-build-release\\CMakeFiles\\ShowIncludes\\foo.h
        
      Found prefix "注意: 包含文件:  "
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-rfkbky"
      binary: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-rfkbky"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-rfkbky'
        
        Run Build Command(s): "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/ninja/win/x64/ninja.exe" -v cmTC_61863
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\cl.exe  /nologo   /DWIN32 /D_WINDOWS  /Ob0 /Od /RTC1 -MDd -Zi /showIncludes /FoCMakeFiles\\cmTC_61863.dir\\CMakeCCompilerABI.c.obj /FdCMakeFiles\\cmTC_61863.dir\\ /FS -c "C:\\Users\\<USER>\\AppData\\Local\\Programs\\CLion 2\\bin\\cmake\\win\\x64\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && "C:\\Users\\<USER>\\AppData\\Local\\Programs\\CLion 2\\bin\\cmake\\win\\x64\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1940 --intdir=CMakeFiles\\cmTC_61863.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_61863.dir\\CMakeCCompilerABI.c.obj  /out:cmTC_61863.exe /implib:cmTC_61863.lib /pdb:cmTC_61863.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/PROGRA~1/MICROS~2/2022/ENTERP~1/VC/Tools/MSVC/1440~1.338/bin/Hostx64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/PROGRA~1/MICROS~2/2022/ENTERP~1/VC/Tools/MSVC/1440~1.338/bin/Hostx64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.40.33811.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-6rnb2k"
      binary: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-6rnb2k"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-6rnb2k'
        
        Run Build Command(s): "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/ninja/win/x64/ninja.exe" -v cmTC_049bc
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP   /DWIN32 /D_WINDOWS /EHsc  /Ob0 /Od /RTC1 -MDd -Zi /showIncludes /FoCMakeFiles\\cmTC_049bc.dir\\CMakeCXXCompilerABI.cpp.obj /FdCMakeFiles\\cmTC_049bc.dir\\ /FS -c "C:\\Users\\<USER>\\AppData\\Local\\Programs\\CLion 2\\bin\\cmake\\win\\x64\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && "C:\\Users\\<USER>\\AppData\\Local\\Programs\\CLion 2\\bin\\cmake\\win\\x64\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1940 --intdir=CMakeFiles\\cmTC_049bc.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_049bc.dir\\CMakeCXXCompilerABI.cpp.obj  /out:cmTC_049bc.exe /implib:cmTC_049bc.lib /pdb:cmTC_049bc.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/PROGRA~1/MICROS~2/2022/ENTERP~1/VC/Tools/MSVC/1440~1.338/bin/Hostx64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/PROGRA~1/MICROS~2/2022/ENTERP~1/VC/Tools/MSVC/1440~1.338/bin/Hostx64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.40.33811.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:15 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-4gp5ch"
      binary: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-4gp5ch"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6;C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-4gp5ch'
        
        Run Build Command(s): "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/ninja/win/x64/ninja.exe" -v cmTC_0e499
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\cl.exe  /nologo -DCMAKE_HAVE_LIBC_PTHREAD  /DWIN32 /D_WINDOWS  /Ob0 /Od /RTC1 -MDd -Zi /showIncludes /FoCMakeFiles\\cmTC_0e499.dir\\src.c.obj /FdCMakeFiles\\cmTC_0e499.dir\\ /FS -c D:\\QtProject\\tailchat_web\\cmake-build-release\\CMakeFiles\\CMakeScratch\\TryCompile-4gp5ch\\src.c
        FAILED: CMakeFiles/cmTC_0e499.dir/src.c.obj 
        C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\cl.exe  /nologo -DCMAKE_HAVE_LIBC_PTHREAD  /DWIN32 /D_WINDOWS  /Ob0 /Od /RTC1 -MDd -Zi /showIncludes /FoCMakeFiles\\cmTC_0e499.dir\\src.c.obj /FdCMakeFiles\\cmTC_0e499.dir\\ /FS -c D:\\QtProject\\tailchat_web\\cmake-build-release\\CMakeFiles\\CMakeScratch\\TryCompile-4gp5ch\\src.c
        D:\\QtProject\\tailchat_web\\cmake-build-release\\CMakeFiles\\CMakeScratch\\TryCompile-4gp5ch\\src.c(1): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:15 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-7zxdqr"
      binary: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-7zxdqr"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6;C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-7zxdqr'
        
        Run Build Command(s): "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/ninja/win/x64/ninja.exe" -v cmTC_dc8e9
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\cl.exe  /nologo   /DWIN32 /D_WINDOWS -DCHECK_FUNCTION_EXISTS=pthread_create /Ob0 /Od /RTC1 -MDd -Zi /showIncludes /FoCMakeFiles\\cmTC_dc8e9.dir\\CheckFunctionExists.c.obj /FdCMakeFiles\\cmTC_dc8e9.dir\\ /FS -c D:\\QtProject\\tailchat_web\\cmake-build-release\\CMakeFiles\\CMakeScratch\\TryCompile-7zxdqr\\CheckFunctionExists.c
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && "C:\\Users\\<USER>\\AppData\\Local\\Programs\\CLion 2\\bin\\cmake\\win\\x64\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1940 --intdir=CMakeFiles\\cmTC_dc8e9.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_dc8e9.dir\\CheckFunctionExists.c.obj  /out:cmTC_dc8e9.exe /implib:cmTC_dc8e9.lib /pdb:cmTC_dc8e9.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        FAILED: cmTC_dc8e9.exe 
        C:\\WINDOWS\\system32\\cmd.exe /C "cd . && "C:\\Users\\<USER>\\AppData\\Local\\Programs\\CLion 2\\bin\\cmake\\win\\x64\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1940 --intdir=CMakeFiles\\cmTC_dc8e9.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_dc8e9.dir\\CheckFunctionExists.c.obj  /out:cmTC_dc8e9.exe /implib:cmTC_dc8e9.lib /pdb:cmTC_dc8e9.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_dc8e9.dir\\CheckFunctionExists.c.obj /out:cmTC_dc8e9.exe /implib:cmTC_dc8e9.lib /pdb:cmTC_dc8e9.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_dc8e9.dir/intermediate.manifest CMakeFiles\\cmTC_dc8e9.dir/manifest.res" failed (exit code 1104) with the following output:
        LINK : fatal error LNK1104: 无法打开文件“pthreads.lib”\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:15 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-e2sfgk"
      binary: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-e2sfgk"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6;C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-e2sfgk'
        
        Run Build Command(s): "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/ninja/win/x64/ninja.exe" -v cmTC_a593c
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\cl.exe  /nologo   /DWIN32 /D_WINDOWS -DCHECK_FUNCTION_EXISTS=pthread_create /Ob0 /Od /RTC1 -MDd -Zi /showIncludes /FoCMakeFiles\\cmTC_a593c.dir\\CheckFunctionExists.c.obj /FdCMakeFiles\\cmTC_a593c.dir\\ /FS -c D:\\QtProject\\tailchat_web\\cmake-build-release\\CMakeFiles\\CMakeScratch\\TryCompile-e2sfgk\\CheckFunctionExists.c
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && "C:\\Users\\<USER>\\AppData\\Local\\Programs\\CLion 2\\bin\\cmake\\win\\x64\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1940 --intdir=CMakeFiles\\cmTC_a593c.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_a593c.dir\\CheckFunctionExists.c.obj  /out:cmTC_a593c.exe /implib:cmTC_a593c.lib /pdb:cmTC_a593c.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        FAILED: cmTC_a593c.exe 
        C:\\WINDOWS\\system32\\cmd.exe /C "cd . && "C:\\Users\\<USER>\\AppData\\Local\\Programs\\CLion 2\\bin\\cmake\\win\\x64\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1940 --intdir=CMakeFiles\\cmTC_a593c.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_a593c.dir\\CheckFunctionExists.c.obj  /out:cmTC_a593c.exe /implib:cmTC_a593c.lib /pdb:cmTC_a593c.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_a593c.dir\\CheckFunctionExists.c.obj /out:cmTC_a593c.exe /implib:cmTC_a593c.lib /pdb:cmTC_a593c.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_a593c.dir/intermediate.manifest CMakeFiles\\cmTC_a593c.dir/manifest.res" failed (exit code 1104) with the following output:
        LINK : fatal error LNK1104: 无法打开文件“pthread.lib”\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/cmake/win/x64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:218 (find_package)"
      - "CMakeLists.txt:15 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-gpv65q"
      binary: "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-gpv65q"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6;C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/CMakeScratch/TryCompile-gpv65q'
        
        Run Build Command(s): "C:/Users/<USER>/AppData/Local/Programs/CLion 2/bin/ninja/win/x64/ninja.exe" -v cmTC_a2a3e
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DHAVE_STDATOMIC  /DWIN32 /D_WINDOWS /EHsc  /Ob0 /Od /RTC1 -std:c++20 -MDd -Zi /showIncludes /FoCMakeFiles\\cmTC_a2a3e.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_a2a3e.dir\\ /FS -c D:\\QtProject\\tailchat_web\\cmake-build-release\\CMakeFiles\\CMakeScratch\\TryCompile-gpv65q\\src.cxx
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && "C:\\Users\\<USER>\\AppData\\Local\\Programs\\CLion 2\\bin\\cmake\\win\\x64\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1940 --intdir=CMakeFiles\\cmTC_a2a3e.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100226~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\ENTERP~1\\VC\\Tools\\MSVC\\1440~1.338\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_a2a3e.dir\\src.cxx.obj  /out:cmTC_a2a3e.exe /implib:cmTC_a2a3e.lib /pdb:cmTC_a2a3e.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...
