{"bmi-installation": null, "compiler-frontend-variant": "MSVC", "compiler-id": "MSVC", "compiler-simulate-id": "", "config": "Release", "cxx-modules": {}, "database-info": null, "dir-cur-bld": "D:/QtProject/tailchat_web/cmake-build-release", "dir-cur-src": "D:/QtProject/tailchat_web", "dir-top-bld": "D:/QtProject/tailchat_web/cmake-build-release", "dir-top-src": "D:/QtProject/tailchat_web", "exports": [], "forward-modules-from-target-dirs": [], "include-dirs": ["tailchat_web_autogen\\include", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "C:\\Qt\\6.9.1\\msvc2022_64\\include", "C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWebEngineWidgets", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWebEngineCore", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtQuick", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtQml", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtQmlIntegration", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtQmlMeta", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtQmlModels", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtQmlWorkerScript", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWebChannel", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtPositioning", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtPrintSupport"], "language": "CXX", "linked-target-dirs": [], "module-dir": "D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/tailchat_web.dir", "sources": {"CMakeFiles/tailchat_web.dir/browser.cpp.obj": {"language": "CXX", "source": "D:/QtProject/tailchat_web/browser.cpp"}, "CMakeFiles/tailchat_web.dir/config.cpp.obj": {"language": "CXX", "source": "D:/QtProject/tailchat_web/config.cpp"}, "CMakeFiles/tailchat_web.dir/main.cpp.obj": {"language": "CXX", "source": "D:/QtProject/tailchat_web/main.cpp"}}}