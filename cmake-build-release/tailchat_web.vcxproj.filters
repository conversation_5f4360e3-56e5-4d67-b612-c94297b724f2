<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\QtProject\tailchat_web\cmake-build-release\tailchat_web_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QtProject\tailchat_web\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QtProject\tailchat_web\browser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QtProject\tailchat_web\config.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QtProject\tailchat_web\cmake-build-release\tailchat_web_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QtProject\tailchat_web\cmake-build-release\tailchat_web_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\QtProject\tailchat_web\cmake-build-release\tailchat_web_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\QtProject\tailchat_web\browser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\QtProject\tailchat_web\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\QtProject\tailchat_web\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{DD11665A-ADCC-30F5-B710-4DA2C70657FD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{C3DA4E0B-ADC6-305B-94B8-378DF520D62E}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
