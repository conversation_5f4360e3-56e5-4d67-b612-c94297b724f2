^D:\QTPROJECT\TAILCHAT_WEB\CMAKE-BUILD-RELEASE\TAILCHAT_WEB.DIR\DEBUG\BROWSER.OBJ|D:\QTPROJECT\TAILCHAT_WEB\CMAKE-BUILD-RELEASE\TAILCHAT_WEB.DIR\DEBUG\CONFIG.OBJ|D:\QTPROJECT\TAILCHAT_WEB\CMAKE-BUILD-RELEASE\TAILCHAT_WEB.DIR\DEBUG\MAIN.OBJ|D:\QTPROJECT\TAILCHAT_WEB\CMAKE-BUILD-RELEASE\TAILCHAT_WEB.DIR\DEBUG\MOCS_COMPILATION_DEBUG.OBJ
/OUT:"D:\QTPROJECT\TAILCHAT_WEB\CMAKE-BUILD-RELEASE\DEBUG\TAILCHAT_WEB.EXE" /INCREMENTAL /ILK:"TAILCHAT_WEB.DIR\DEBUG\TAILCHAT_WEB.ILK" /NOLOGO C:\QT\6.9.1\MSVC2022_64\LIB\QT6CORED.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6GUID.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6WIDGETSD.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6WEBENGINEWIDGETSD.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6NETWORKD.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6WEBENGINECORED.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6WEBCHANNELD.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6POSITIONINGD.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6PRINTSUPPORTD.LIB COMDLG32.LIB WINSPOOL.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6QUICKD.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6QMLMETAD.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6QMLWORKERSCRIPTD.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6QMLMODELSD.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6OPENGLD.LIB D3D11.LIB DXGI.LIB DXGUID.LIB D3D12.LIB USER32.LIB C:\QT\6.9.1\MSVC2022_64\LIB\QT6QMLD.LIB WS2_32.LIB MPR.LIB USERENV.LIB SHELL32.LIB KERNEL32.LIB USER32.LIB GDI32.LIB WINSPOOL.LIB SHELL32.LIB OLE32.LIB OLEAUT32.LIB UUID.LIB COMDLG32.LIB ADVAPI32.LIB /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/QTPROJECT/TAILCHAT_WEB/CMAKE-BUILD-RELEASE/DEBUG/TAILCHAT_WEB.PDB" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/QTPROJECT/TAILCHAT_WEB/CMAKE-BUILD-RELEASE/DEBUG/TAILCHAT_WEB.LIB" /MACHINE:X64  /machine:x64 TAILCHAT_WEB.DIR\DEBUG\MOCS_COMPILATION_DEBUG.OBJ
TAILCHAT_WEB.DIR\DEBUG\MAIN.OBJ
TAILCHAT_WEB.DIR\DEBUG\BROWSER.OBJ
TAILCHAT_WEB.DIR\DEBUG\CONFIG.OBJ
