^D:\QTPROJECT\TAILCHAT_WEB\CMAKELISTS.TXT
setlocal
"C:\Users\<USER>\AppData\Local\Programs\CLion 2\bin\cmake\win\x64\bin\cmake.exe" -SD:/QtProject/tailchat_web -BD:/QtProject/tailchat_web/cmake-build-release --check-stamp-file D:/QtProject/tailchat_web/cmake-build-release/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
