^D:\QTPROJECT\TAILCHAT_WEB\MAIN.CPP
/c /Zi /nologo /W1 /WX- /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D QT_CORE_LIB /D _ENABLE_EXTENDED_ALIGNED_STORAGE /D WIN64 /D _WIN64 /D UNICODE /D _UNICODE /D QT_GUI_LIB /D QT_WIDGETS_LIB /D QT_WEBENGINEWIDGETS_LIB /D QT_WEBENGINECORE_LIB /D QT_NETWORK_LIB /D QT_QUICK_LIB /D QT_QML_LIB /D QT_QMLINTEGRATION_LIB /D QT_QMLMETA_LIB /D QT_QMLMODELS_LIB /D QT_QMLWORKERSCRIPT_LIB /D QT_OPENGL_LIB /D QT_WEBCHANNEL_LIB /D QT_POSITIONING_LIB /D QT_PRINTSUPPORT_LIB /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /permissive- /ifcOutput "TAILCHAT_WEB.DIR\DEBUG\\" /scanDependencies "TAILCHAT_WEB.DIR\DEBUG\\" /Fo"TAILCHAT_WEB.DIR\DEBUG\\" /Fd"TAILCHAT_WEB.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/QtProject/tailchat_web/cmake-build-release/tailchat_web_autogen/include_Debug" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtCore" /external:I "C:/Qt/6.9.1/msvc2022_64/include" /external:I "C:/Qt/6.9.1/msvc2022_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtGui" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWidgets" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWebEngineWidgets" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWebEngineCore" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtNetwork" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQuick" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQml" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlIntegration" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlMeta" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlModels" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlWorkerScript" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtOpenGL" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWebChannel" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtPositioning" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtPrintSupport" /utf-8 -Zc:__cplusplus -utf-8 D:\QTPROJECT\TAILCHAT_WEB\MAIN.CPP
^D:\QTPROJECT\TAILCHAT_WEB\BROWSER.CPP
/c /Zi /nologo /W1 /WX- /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D QT_CORE_LIB /D _ENABLE_EXTENDED_ALIGNED_STORAGE /D WIN64 /D _WIN64 /D UNICODE /D _UNICODE /D QT_GUI_LIB /D QT_WIDGETS_LIB /D QT_WEBENGINEWIDGETS_LIB /D QT_WEBENGINECORE_LIB /D QT_NETWORK_LIB /D QT_QUICK_LIB /D QT_QML_LIB /D QT_QMLINTEGRATION_LIB /D QT_QMLMETA_LIB /D QT_QMLMODELS_LIB /D QT_QMLWORKERSCRIPT_LIB /D QT_OPENGL_LIB /D QT_WEBCHANNEL_LIB /D QT_POSITIONING_LIB /D QT_PRINTSUPPORT_LIB /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /permissive- /ifcOutput "TAILCHAT_WEB.DIR\DEBUG\\" /scanDependencies "TAILCHAT_WEB.DIR\DEBUG\\" /Fo"TAILCHAT_WEB.DIR\DEBUG\\" /Fd"TAILCHAT_WEB.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/QtProject/tailchat_web/cmake-build-release/tailchat_web_autogen/include_Debug" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtCore" /external:I "C:/Qt/6.9.1/msvc2022_64/include" /external:I "C:/Qt/6.9.1/msvc2022_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtGui" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWidgets" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWebEngineWidgets" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWebEngineCore" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtNetwork" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQuick" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQml" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlIntegration" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlMeta" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlModels" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlWorkerScript" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtOpenGL" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWebChannel" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtPositioning" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtPrintSupport" /utf-8 -Zc:__cplusplus -utf-8 D:\QTPROJECT\TAILCHAT_WEB\BROWSER.CPP
^D:\QTPROJECT\TAILCHAT_WEB\CONFIG.CPP
/c /Zi /nologo /W1 /WX- /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D QT_CORE_LIB /D _ENABLE_EXTENDED_ALIGNED_STORAGE /D WIN64 /D _WIN64 /D UNICODE /D _UNICODE /D QT_GUI_LIB /D QT_WIDGETS_LIB /D QT_WEBENGINEWIDGETS_LIB /D QT_WEBENGINECORE_LIB /D QT_NETWORK_LIB /D QT_QUICK_LIB /D QT_QML_LIB /D QT_QMLINTEGRATION_LIB /D QT_QMLMETA_LIB /D QT_QMLMODELS_LIB /D QT_QMLWORKERSCRIPT_LIB /D QT_OPENGL_LIB /D QT_WEBCHANNEL_LIB /D QT_POSITIONING_LIB /D QT_PRINTSUPPORT_LIB /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /permissive- /ifcOutput "TAILCHAT_WEB.DIR\DEBUG\\" /scanDependencies "TAILCHAT_WEB.DIR\DEBUG\\" /Fo"TAILCHAT_WEB.DIR\DEBUG\\" /Fd"TAILCHAT_WEB.DIR\DEBUG\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/QtProject/tailchat_web/cmake-build-release/tailchat_web_autogen/include_Debug" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtCore" /external:I "C:/Qt/6.9.1/msvc2022_64/include" /external:I "C:/Qt/6.9.1/msvc2022_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtGui" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWidgets" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWebEngineWidgets" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWebEngineCore" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtNetwork" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQuick" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQml" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlIntegration" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlMeta" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlModels" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtQmlWorkerScript" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtOpenGL" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtWebChannel" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtPositioning" /external:I "C:/Qt/6.9.1/msvc2022_64/include/QtPrintSupport" /utf-8 -Zc:__cplusplus -utf-8 D:\QTPROJECT\TAILCHAT_WEB\CONFIG.CPP
