{"Version": "1.2", "Data": {"Source": "d:\\qtproject\\tailchat_web\\browser.cpp", "ProvidedModule": "", "Includes": ["d:\\qtproject\\tailchat_web\\browser.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qmainwindow", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qmainwindow.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qtwidgetsglobal.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtguiglobal.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qglobal.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\type_traits", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\yvals_core.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\vcruntime.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\sal.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\concurrencysal.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\vadefs.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xkeycheck.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cstddef", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stddef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xtr1common", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cstdint", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\stdint.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\utility", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\initializer_list", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\compare", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\concepts", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\vcruntime_new.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\assert.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\stdbool.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtcoreglobal.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtversionchecks.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtconfiginclude.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\version", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qconfig.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtcore-config.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtconfigmacros.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtdeprecationdefinitions.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qprocessordetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsystemdetection.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\assert.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtcoreexports.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtdeprecationmarkers.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtclasshelpermacros.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtpreprocessorsupport.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsystemdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qprocessordetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qassert.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtnoop.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtypes.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qprocessordetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsystemdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtversion.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtypeinfo.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainerfwd.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\limits", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cfloat", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\float.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\climits", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\limits.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cwchar", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cstdio", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stdio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstdio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_stdio_config.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\wchar.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_memcpy_s.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\errno.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\vcruntime_string.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wconio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wctype.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wdirect.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_share.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wprocess.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstdlib.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstring.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wtime.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\sys\\stat.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\sys\\types.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\intrin0.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\intrin0.inl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsysinfo.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qprocessordetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qlogging.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qflags.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompare_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\algorithm", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_minmax.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xmemory", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cstdlib", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\math.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_math.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stdlib.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_malloc.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_search.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\new", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\exception", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\yvals.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\crtdbg.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\vcruntime_new_debug.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\crtdefs.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\use_ansi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\malloc.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\vcruntime_exception.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\eh.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_terminate.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xatomic.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xutility", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_iter_core.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cstring", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\string.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_memory.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\tuple", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qatomic.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qbasicatomic.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qatomic_cxx11.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qgenericatomic.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qyieldcpu.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qprocessordetection.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\atomic", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xatomic_wait.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xthreads.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xtimec.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\ctime", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\time.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qconstructormacros.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qdarwinhelpers.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qexceptionhandling.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qforeach.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qttypetraits.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\optional", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xsmf_control.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\variant", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qfunctionpointer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qglobalstatic.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qmalloc.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qminmax.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qnumeric.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cmath", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\intrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\setjmp.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\immintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\wmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\nmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\smmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\tmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\pmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\emmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\mmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\zmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\ammintrin.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qoverload.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qswap.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtenvironmentvariables.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtresource.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qttranslation.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qversiontagging.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtgui-config.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtguiexports.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qtwidgets-config.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qtwidgetsexports.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qwidget.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qwindowdefs.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qobjectdefs.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qnamespace.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompare.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstdlibdetection.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\bit", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_bit_utils.hpp", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcomparehelpers.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\q20type_traits.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsystemdetection.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\functional", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\typeinfo", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\vcruntime_typeinfo.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\unordered_map", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xhash", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\list", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xpolymorphic_allocator.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\vector", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_sanitizer_annotate_container.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xbit_ops.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xnode_handle.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtmetamacros.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qobjectdefs_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qfunctionaltools_impl.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\memory", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\iosfwd", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qwindowdefs_win.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qobject.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstring.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qchar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringview.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qchar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringview.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qbytearray.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qrefcount.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qarraydata.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qpair.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qarraydatapointer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qarraydataops.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainertools_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qxptype_traits.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\iterator", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainertools_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\q20functional.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\q20memory.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\q17memory.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qbytearrayalgorithms.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\stdarg.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qbytearrayview.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringfwd.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\string", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xstring", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cctype", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\ctype.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\string_view", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringfwd.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringliteral.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringalgorithms.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringfwd.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qlatin1stringview.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qchar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringfwd.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qanystringview.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qutf8stringview.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringfwd.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringtokenizer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringbuilder.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstring.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringconverter.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringconverter_base.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringfwd.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstring.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringbuilder.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstring.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qlist.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qhashfunctions.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringfwd.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\numeric", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qiterator.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainertools_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainertools_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qbytearraylist.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qlist.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringlist.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qlist.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qalgorithms.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainertools_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringmatcher.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qscopedpointer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qmetatype.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qdatastream.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qiodevicebase.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qfloat16.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qmath.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtformat_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsystemdetection.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\format", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_format_ucd_tables.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_formatter.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_print.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xfilesystem_abi.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\charconv", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xcharconv.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xerrc.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xcharconv_ryu.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xcharconv_ryu_tables.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xcharconv_tables.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\locale", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xlocbuf", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\streambuf", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xiosbase", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\share.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\system_error", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_system_error_abi.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cerrno", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\stdexcept", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xcall_once.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xlocale", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xfacet", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xlocinfo", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_xlocinfo_types.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\clocale", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\locale.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xlocmes", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xlocmon", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xlocnum", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xloctime", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qiterable.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qmetacontainer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainerinfo.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtaggedpointer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qscopeguard.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\array", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\map", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\xtree", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qobject_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qbindingstorage.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\chrono", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_chrono.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\ratio", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\__msvc_tzdb.hpp", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\forward_list", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\iomanip", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\istream", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\ostream", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\ios", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\sstream", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qmargins.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\q23utility.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\q20utility.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qaction.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qkeysequence.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qicon.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsize.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpixmap.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpaintdevice.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qrect.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qpoint.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qcolor.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qrgb.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qprocessordetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringlist.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qrgba64.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qprocessordetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qshareddata.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qimage.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpixelformat.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtransform.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpolygon.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qregion.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qspan.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\cassert", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\assert.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\q20iterator.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\span", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qline.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qvariant.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qdebug.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtextstream.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qchar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontiguouscache.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsharedpointer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsharedpointer_impl.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\set", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\unordered_set", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qmap.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qshareddata_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qset.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qhash.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainertools_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainertools_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qvarlengtharray.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcontainertools_impl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstringlist.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qbytearraylist.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpalette.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qbrush.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qfont.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qendian.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qfontmetrics.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qfontinfo.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qfontvariableaxis.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qsizepolicy.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qcursor.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qbitmap.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qtabwidget.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginewidgets\\qwebengineview", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginewidgets\\qwebengineview.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpagelayout", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpagelayout.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpagesize.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpageranges.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginewidgets\\qtwebenginewidgetsglobal.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginecore\\qwebenginepage.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginecore\\qtwebenginecoreglobal.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginecore\\qtwebenginecore-config.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginecore\\qwebengineclientcertificateselection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtnetwork\\qtnetwork-config.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtnetwork\\qsslcertificate.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtnetwork\\qtnetworkglobal.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtcoreglobal.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtnetwork\\qtnetwork-config.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtnetwork\\qtnetworkexports.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcryptographichash.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qdatetime.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcalendar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qlocale.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtnetwork\\qssl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qflags", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginecore\\qwebenginedownloadrequest.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qurl.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginecore\\qwebenginequotarequest.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginecore\\qwebengineframe.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtqml\\qqmlregistration.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtqmlintegration\\qqmlintegration.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtqml\\qjsvalue.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtqml\\qtqmlglobal.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtqml\\qtqml-config.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtqml\\qtqmlexports.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcompilerdetection.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qlist", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsizef", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstring", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qurl", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qweakpointer", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwebenginecore\\qwebenginepermission.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtgui-config.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qsystemtrayicon", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qsystemtrayicon.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qmenu", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qmenu.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qaction", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qvboxlayout", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qboxlayout.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qlayout.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qlayoutitem.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qboxlayout.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qgridlayout.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qlayout.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qhboxlayout", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qpushbutton", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qpushbutton.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qabstractbutton.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qlineedit", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qlineedit.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qframe.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtextcursor.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtextdocument.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtextformat.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpen.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtextoption.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qchar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qshortcut", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qshortcut.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qcloseevent", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qevent.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcoreevent.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qbasictimer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qabstracteventdispatcher.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qeventloop.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qdeadlinetimer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qelapsedtimer.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qiodevice.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qeventpoint.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qvector2d.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qvectornd.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qpointingdevice.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qinputdevice.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qscreen.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qlist", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qobject", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qrect", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsize", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsizef", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qtransform", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qnativeinterface.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qscreen_platform.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qguiapplication.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcoreapplication.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcoreapplication_platform.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qcoreapplication.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qinputmethod.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qguiapplication_platform.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qguiapplication.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qhideevent", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qshowevent", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qprogressbar", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qprogressbar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtimer", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtimer.h", "d:\\qtproject\\tailchat_web\\config.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qstring", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsettings", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qsettings.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\windows.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\winapifamily.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\winpackagefamily.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\sdkddkver.h", "c:\\program files\\microsoft visual studio\\2022\\enterprise\\vc\\tools\\msvc\\14.40.33807\\include\\excpt.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\windef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\minwindef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\specstrings.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\specstrings_strict.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\specstrings_undef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\driverspecs.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\sdv_driverspecs.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winnt.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\kernelspecs.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\basetsd.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\guiddef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack4.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack4.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack4.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\apiset.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\ktmtypes.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winbase.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\apisetcconv.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\minwinbase.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\apiquery2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\processenv.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\fileapifromapp.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\fileapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\debugapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\utilapiset.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\handleapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\errhandlingapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\fibersapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\namedpipeapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\profileapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\heapapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\ioapiset.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\synchapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\interlockedapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\processthreadsapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\sysinfoapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\memoryapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\enclaveapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\threadpoollegacyapiset.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\threadpoolapiset.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\jobapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\jobapi2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\wow64apiset.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\libloaderapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\securitybaseapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\namespaceapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\systemtopologyapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\processtopologyapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\securityappcontainer.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\realtimeapiset.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\winerror.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\timezoneapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\wingdi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack4.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack4.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winuser.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\tvout.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winnls.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\datetimeapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\stringapiset.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winnls.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\wincon.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\wincontypes.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\consoleapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\consoleapi2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\consoleapi3.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winver.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\verrsrc.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winreg.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\reason.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winnetwk.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\wnnc.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\cderr.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\dde.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\ddeml.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\dlgs.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\lzexpand.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmsystem.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmsyscom.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mciapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmsyscom.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmiscapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmsyscom.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmiscapi2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmsyscom.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\playsoundapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmsyscom.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmeapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmsyscom.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\timeapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmsyscom.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\joystickapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mmsyscom.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\nb30.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\rpc.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\rpcdce.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\rpcdcep.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\rpcnsi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\rpcnterr.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\rpcasync.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\shellapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winperf.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winsock.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\inaddr.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\wincrypt.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\bcrypt.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\ncrypt.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\dpapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winefs.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winscard.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\wtypes.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\rpcndr.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\rpcnsip.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\rpcsal.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\wtypesbase.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\guiddef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winioctl.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack1.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\winsmcrd.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winspool.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\prsht.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\ole2.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\objbase.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\combaseapi.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\unknwnbase.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\objidlbase.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\guiddef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\cguid.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\coml2api.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\objidl.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\unknwn.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\propidlbase.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\oaidl.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\urlmon.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\oleidl.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\servprov.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\msxml.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\propidl.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\oleauto.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\pshpack8.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\poppack.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\commdlg.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\prsht.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\stralign.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\winsvc.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\mcx.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\imm.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\um\\ime_cmodes.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qapplication", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qapplication.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qmessagebox", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qmessagebox.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qdialog.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qdialogbuttonbox.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qkeysequence", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qurl", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qmenubar", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qmenubar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qstatusbar", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qstatusbar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qtoolbar", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qtoolbar.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qvboxlayout", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qhboxlayout", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qwidget", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qinputdialog", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtwidgets\\qinputdialog.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qdesktopservices", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qdesktopservices.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qtimer", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qwindow", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qwindow.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qobject", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qevent", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qmargins", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtcore\\qrect", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qsurface.h", "c:\\qt\\6.9.1\\msvc2022_64\\include\\qtgui\\qsurfaceformat.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}