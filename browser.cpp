#include "browser.h"
#include <QApplication>
#include <QMessageBox>
#include <QKeySequence>
#include <QUrl>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QWidget>
#include <QInputDialog>
#include <QDesktopServices>
#include <QTimer>
#include <QPainter>
#ifdef Q_OS_WIN
#include <QWindow>
#endif

Browser::Browser(QWidget *parent)
    : QMainWindow(parent), config(new Config()), isHiddenToTray(false)
{
    setupUI(); // 先创建UI（包括webView）
    setupBrowserProfile(); // 然后再设置浏览器配置文件
    urlEdit->hide();
    setupTrayIcon();
    setupShortcuts();
    setupStyle();
    createMenuBar();

    // 创建辅助窗口，用于保持热键监听 - 使用更强的窗口类型
    helperWindow = new QWidget(nullptr, Qt::Window | Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    helperWindow->setGeometry(-100, -100, 10, 10); // 放在屏幕外
    helperWindow->setWindowTitle("TailchatHelper");
    // 添加事件过滤器，确保辅助窗口能捕获消息
    helperWindow->installEventFilter(this);
    helperWindow->show();

    // 创建全局热键管理器
    hotkeyManager = new HotkeyMan(this);
    hotkeyManager->registerHotkey(config->getHotkey(), [this]() {
        // 这个回调函数会在热键被按下时执行
        qDebug() << "通过HotkeyMan接收到全局热键";
        QTimer::singleShot(0, this, &Browser::toggleVisibility);
    });

    // 加载默认网页
    webView->load(QUrl(config->getDefaultUrl()));

    // 设置窗口属性
    setWindowTitle("TailChat Web Browser");
    setMinimumSize(10, 10);
    resize(1200, 800);

    // 检查是否需要启动时隐藏
    if (config->getStartHidden()) {
        hide();
        isHiddenToTray = true;
    }
}

Browser::~Browser()
{
    delete helperWindow; // 删除辅助窗口
    delete config;
}

void Browser::setupUI() {
    // 创建中央窗口部件
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // 创建工具栏布局
    QHBoxLayout *toolbarLayout = new QHBoxLayout();

    // 创建URL输入框
    urlEdit = new QLineEdit();
    urlEdit->setPlaceholderText("输入网址...");

    // 创建刷新按钮
    refreshButton = new QPushButton("刷新");
    refreshButton->setMaximumWidth(60);

    // 创建设置按钮
    settingsButton = new QPushButton("设置");
    settingsButton->setMaximumWidth(60);

    // 添加到工具栏布局
    toolbarLayout->addWidget(urlEdit);
    toolbarLayout->addWidget(refreshButton);
    toolbarLayout->addWidget(settingsButton);

    // 创建进度条
    progressBar = new QProgressBar();
    progressBar->setVisible(false);
    progressBar->setMaximumHeight(4);

    // 创建WebView
    webView = new QWebEngineView();

    // 添加到主布局
    // mainLayout->addLayout(toolbarLayout);
    mainLayout->addWidget(progressBar);
    mainLayout->addWidget(webView);

    // 连接信号槽
    connect(refreshButton, &QPushButton::clicked, this, &Browser::refreshPage);
    connect(settingsButton, &QPushButton::clicked, this, &Browser::showSettings);
    connect(urlEdit, &QLineEdit::returnPressed, this, &Browser::navigateToUrl);
    connect(webView, &QWebEngineView::urlChanged, this, &Browser::onUrlChanged);
    connect(webView, &QWebEngineView::loadProgress, this, &Browser::onLoadProgress);
    connect(webView, &QWebEngineView::loadFinished, this, &Browser::onLoadFinished);
}

void Browser::setupTrayIcon() {
    // 创建系统托盘图标
    trayIcon = new QSystemTrayIcon(this);

    // 创建一个简单的T字图标
    QPixmap pixmap(16, 16);
    pixmap.fill(Qt::transparent);
    QPainter painter(&pixmap);
    painter.setPen(Qt::blue);
    painter.setBrush(Qt::blue);
    painter.drawLine(8, 0, 8, 16); // 垂直线
    painter.drawLine(0, 8, 16, 8); // 水平线
    painter.end();
    // 设置托盘图标
    trayIcon->setIcon(QIcon(pixmap));

    // 创建托盘菜单
    trayMenu = new QMenu(this);

    QAction *showAction = trayMenu->addAction("显示窗口");
    QAction *hideAction = trayMenu->addAction("隐藏窗口");
    trayMenu->addSeparator();
    QAction *refreshAction = trayMenu->addAction("刷新页面");
    trayMenu->addSeparator();
    QAction *quitAction = trayMenu->addAction("退出");

    trayIcon->setContextMenu(trayMenu);

    // 连接信号槽
    connect(showAction, &QAction::triggered, this, &QWidget::show);
    connect(hideAction, &QAction::triggered, this, &Browser::toggleVisibility);
    connect(refreshAction, &QAction::triggered, this, &Browser::refreshPage);
    connect(quitAction, &QAction::triggered, qApp, &QApplication::quit);
    connect(trayIcon, &QSystemTrayIcon::activated, this, &Browser::trayIconActivated);

    trayIcon->show();
    // trayIcon->showMessage("TailChat Web Browser", "程序已启动，使用 " + config->getHotkey() + " 快捷键切换显示/隐藏");
}

void Browser::setupShortcuts()
{
    // 在Windows上我们使用原生热键，所以这里只创建一个备用的Qt快捷键
    toggleShortcut = new QShortcut(QKeySequence(config->getHotkey()), this);
    toggleShortcut->setContext(Qt::WindowShortcut);
    connect(toggleShortcut, &QShortcut::activated, this, &Browser::toggleVisibility);
}

void Browser::setupStyle() {
    // 设置灰色主题
    setStyleSheet(R"(
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QLineEdit {
            background-color: #3c3c3c;
            border: 1px solid #555555;
            padding: 8px;
            border-radius: 4px;
            color: #ffffff;
            font-size: 12px;
        }
        QLineEdit:focus {
            border-color: #0078d4;
        }
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            padding: 8px 16px;
            border-radius: 4px;
            color: #ffffff;
            font-size: 12px;
        }
        QPushButton:hover {
            background-color: #4a4a4a;
        }
        QPushButton:pressed {
            background-color: #353535;
        }
        QMenuBar {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
        }
        QMenuBar::item:selected {
            background-color: #404040;
        }
        QMenu {
            background-color: #2b2b2b;
            color: #ffffff;
            border: 1px solid #555555;
        }
        QMenu::item:selected {
            background-color: #404040;
        }
        QProgressBar {
            border: none;
            background-color: #3c3c3c;
        }
        QProgressBar::chunk {
            background-color: #0078d4;
        }
    )");
}

void Browser::createMenuBar() {
    QMenuBar *menuBar = this->menuBar();

    // 文件菜单
    QMenu *fileMenu = menuBar->addMenu("文件");
    fileMenu->addAction("新建窗口", [this]() {
        Browser *newBrowser = new Browser();
        newBrowser->show();
    });
    fileMenu->addSeparator();
    fileMenu->addAction("退出", qApp, &QApplication::quit);

    // 视图菜单
    QMenu *viewMenu = menuBar->addMenu("视图");
    viewMenu->addAction("刷新", this, &Browser::refreshPage, QKeySequence::Refresh);
    viewMenu->addAction("全屏", this, &QWidget::showFullScreen, QKeySequence("F11"));
    viewMenu->addAction("退出全屏", this, &QWidget::showNormal, QKeySequence("Esc"));

    // 导航菜单
    QMenu *navMenu = menuBar->addMenu("导航");
    navMenu->addAction("后退", webView, &QWebEngineView::back, QKeySequence::Back);
    navMenu->addAction("前进", webView, &QWebEngineView::forward, QKeySequence::Forward);
    navMenu->addAction("首页", [this]() { webView->load(QUrl(config->getDefaultUrl())); });

    // 工具菜单
    QMenu *toolsMenu = menuBar->addMenu("工具");
    toolsMenu->addAction("设置", this, &Browser::showSettings);
    toolsMenu->addAction("开发者工具", [this]() {
        // 注意：需要在实际部署时启用开发者工具
        // webView->page()->setDevToolsPage(new QWebEnginePage());
    });
}

void Browser::toggleVisibility()
{
    qDebug() << "toggleVisibility被调用，当前状态:" << (isVisible() ? "可见" : "隐藏") << "isHiddenToTray:" << isHiddenToTray;

    if (!isHiddenToTray && isVisible()) {
        hide();
        isHiddenToTray = true;
        // trayIcon->showMessage("TailChat Web Browser", "窗口已隐藏到系统托盘");
        qDebug() << "窗口被隐藏";
    } else {
        qDebug() << "尝试显示窗口";
        show();
        raise();
        activateWindow();
        setWindowState(windowState() & ~Qt::WindowMinimized);
        isHiddenToTray = false;

        // 确保窗口显示在最前面
        setWindowFlags(windowFlags() | Qt::WindowStaysOnTopHint);
        show();
        setWindowFlags(windowFlags() & ~Qt::WindowStaysOnTopHint);
        show();
        qDebug() << "窗口已显示";
    }
}

void Browser::trayIconActivated(QSystemTrayIcon::ActivationReason reason) {
    if (reason == QSystemTrayIcon::DoubleClick) {
        toggleVisibility();
    }
}

void Browser::refreshPage() {
    webView->reload();
}

void Browser::navigateToUrl() {
    QString url = urlEdit->text().trimmed();
    if (!url.isEmpty()) {
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
        }
        webView->load(QUrl(url));
    }
}

void Browser::onUrlChanged(const QUrl &url) {
    urlEdit->setText(url.toString());
}

void Browser::onLoadProgress(int progress) {
    if (progress < 100) {
        progressBar->setVisible(true);
        progressBar->setValue(progress);
    } else {
        progressBar->setVisible(false);
    }
}

void Browser::onLoadFinished(bool success) {
    progressBar->setVisible(false);
    if (!success) {
        setWindowTitle("TailChat Web Browser - 加载失败");
    } else {
        setWindowTitle("TailChat Web Browser - " + webView->title());
    }
}

void Browser::showSettings()
{
    bool ok;
    QString newUrl = QInputDialog::getText(this, "设置默认网址",
                                          "请输入默认网址:", QLineEdit::Normal,
                                          config->getDefaultUrl(), &ok);
    if (ok && !newUrl.isEmpty()) {
        config->setDefaultUrl(newUrl);
        QMessageBox::information(this, "设置", "默认网址已更新为: " + newUrl);
    }

    QString newHotkey = QInputDialog::getText(this, "设置快捷键",
                                             "请输入切换显示/隐藏的快捷键 (如: Ctrl+Alt+T):",
                                             QLineEdit::Normal,
                                             config->getHotkey(), &ok);
    if (ok && !newHotkey.isEmpty()) {
        config->setHotkey(newHotkey);

        QMessageBox::information(this, "设置", "快捷键已更新为: " + newHotkey);
    }
}

void Browser::closeEvent(QCloseEvent *event) {
    if (trayIcon->isVisible()) {
        hide();
        event->ignore();
        isHiddenToTray = true;
        // trayIcon->showMessage("TailChat Web Browser", "程序已最小化到系统托盘");
    }
}

void Browser::hideEvent(QHideEvent *event) {
    Q_UNUSED(event)
    isHiddenToTray = true;
}

void Browser::showEvent(QShowEvent *event) {
    Q_UNUSED(event)
    isHiddenToTray = false;
}

bool Browser::eventFilter(QObject *watched, QEvent *event)
{
    // 如果是来自辅助窗口的事件
    if (watched == helperWindow) {
        // 处理任何与热键相关的事件
        if (event->type() == QEvent::KeyPress) {
            QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
            qDebug() << "辅助窗口接收到按键事件:" << keyEvent->key();

            // 您可以在这里添加特定按键的处理逻辑
        }
    }
    return QMainWindow::eventFilter(watched, event);
}

void Browser::setupBrowserProfile() {
    // 创建持久化的WebEngine配置文件，使用应用程序名作为配置文件名
    webProfile = new QWebEngineProfile("TailChat", this);

    // 启用持久化Cookie存储
    QWebEngineCookieStore *cookieStore = webProfile->cookieStore();

    // 配置HTTP缓存
    webProfile->setHttpCacheType(QWebEngineProfile::DiskHttpCache);
    webProfile->setPersistentCookiesPolicy(QWebEngineProfile::AllowPersistentCookies);

    // 设置缓存大小 (50MB)
    webProfile->setHttpCacheMaximumSize(50 * 1024 * 1024);

    // 允许保存密码
    webProfile->setSpellCheckEnabled(false); // 关闭拼写检查以避免不必要的弹窗

    // 开启本地存储(localStorage)、IndexedDB等Web存储功能
    // 这些在默认情况下已经启用，但为了确保，我们显式设置
    webProfile->settings()->setAttribute(QWebEngineSettings::LocalStorageEnabled, true);
    webProfile->settings()->setAttribute(QWebEngineSettings::JavascriptEnabled, true);

    // 创建使用配置文件的WebEnginePage并将其应用到WebView上
    QWebEnginePage *page = new QWebEnginePage(webProfile, webView);
    webView->setPage(page);
}
