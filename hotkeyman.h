#ifndef HOTKEYMAN_H
#define HOTKEYMAN_H

#include <QObject>
#include <QDebug>
#include <QTimer>
#include <functional>

#ifdef Q_OS_WIN
#include <Windows.h>
#endif

class HotkeyMan final : public QObject
{
    Q_OBJECT

public:
    explicit HotkeyMan(QObject *parent = nullptr);
    ~HotkeyMan();

    // 注册全局热键
    bool registerHotkey(const QString &hotkeyStr, const std::function<void()> &callback);
    // 注销全局热键
    void unregisterHotkey();

signals:
    void hotkeyPressed(); // 当热键被按下时发出信号

public:
    bool nativeEventFilter(const QByteArray &eventType, void *message, qintptr *result);

private:
    std::function<void()> m_callback;  // 热键回调函数
    bool m_registered;                 // 热键是否已注册

#ifdef Q_OS_WIN
    static const int HOTKEY_ID = 1;    // 热键ID
    HWND m_hwnd;                       // 窗口句柄

    // 解析热键字符串为Windows API使用的修饰符和虚拟键码
    void parseHotkeyString(const QString &hotkeyStr, UINT &modifiers, UINT &vk);
#endif
};

#endif // HOTKEYMAN_H
