#ifndef CONFIG_H
#define CONFIG_H

#include <QString>
#include <QSettings>

class Config
{
public:
    Config();

    QString getDefaultUrl() const;
    void setDefaultUrl(const QString &url);

    QString getHotkey() const;
    void setHotkey(const QString &hotkey);

    bool getStartHidden() const;
    void setStartHidden(bool hidden);

private:
    QSettings *settings;
    void createDefaultConfig();
};

#endif // CONFIG_H
