#include "config.h"
#include <QStandardPaths>
#include <QDir>

Config::Config()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::ConfigLocation);
    QDir().mkpath(configPath);
    settings = new QSettings(configPath + "/tailchat_web.ini", QSettings::IniFormat);

    // 创建默认配置
    createDefaultConfig();
}

QString Config::getDefaultUrl() const
{
    return settings->value("browser/defaultUrl", "https://www.baidu.com").toString();
}

void Config::setDefaultUrl(const QString &url)
{
    settings->setValue("browser/defaultUrl", url);
    settings->sync();
}

QString Config::getHotkey() const
{
    return settings->value("hotkey/toggleVisible", "Ctrl+Alt+T").toString();
}

void Config::setHotkey(const QString &hotkey)
{
    settings->setValue("hotkey/toggleVisible", hotkey);
    settings->sync();
}

bool Config::getStartHidden() const
{
    return settings->value("window/startHidden", false).toBool();
}

void Config::setStartHidden(bool hidden)
{
    settings->setValue("window/startHidden", hidden);
    settings->sync();
}

void Config::createDefaultConfig()
{
    if (!settings->contains("browser/defaultUrl")) {
        setDefaultUrl("https://chat1.horrz.me:8099");
    }
    if (!settings->contains("hotkey/toggleVisible")) {
        setHotkey("Ctrl+F1");
    }
    if (!settings->contains("window/startHidden")) {
        setStartHidden(false);
    }
}
